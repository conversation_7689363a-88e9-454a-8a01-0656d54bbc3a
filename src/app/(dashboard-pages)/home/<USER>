import {
  <PERSON><PERSON><PERSON>rumb,
  <PERSON>readcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"

// Updated imports to point to src/components/dashboard
import { StrackrOverview } from "@/components/dashboard/strackr-overview"
import { ShopMyOverview } from "@/components/dashboard/shopmy-overview"

// Import the actions
import {
  getPlatformAnalyticsData,
} from "./actions";
import { createClient } from "@/supabase/client/server";

// Make the component async
export default async function HomePage() {
  // Add authentication check first
  const supabase = await createClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error('HomePage auth check failed:', authError);
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-lg font-semibold mb-2">Authentication Required</h2>
          <p className="text-muted-foreground">Please sign in to access the dashboard.</p>
        </div>
      </div>
    );
  }

  // Fetch analytics data for both platforms
  const { data: strackrData, error: strackrError } = await getPlatformAnalyticsData('strackr', '30d');
  const { data: shopMyData, error: shopMyError } = await getPlatformAnalyticsData('shopmy', '30d');

  // TODO: Add error handling display if needed
  if (strackrError) console.error("Strackr Analytics Error:", strackrError);
  if (shopMyError) console.error("ShopMy Analytics Error:", shopMyError);

  return (
    <div className="@container/main flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Breadcrumbs and Separator first (or wherever they were originally) */}
      <Breadcrumb className="hidden font-medium md:flex">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Overview</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Separator className="my-2 hidden md:block" />

      {/* Two-column grid for Strackr and ShopMy analytics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        {/* Column 1: Strackr Overview */}
        <StrackrOverview
          analyticsData={strackrData}
          loading={false}
          error={strackrError}
        />

        {/* Column 2: ShopMy Overview */}
        <ShopMyOverview
          analyticsData={shopMyData}
          loading={false}
          error={shopMyError}
        />
      </div>
    </div>
  );
}
